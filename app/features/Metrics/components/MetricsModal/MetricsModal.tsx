import React, { useEffect, useState } from "react";
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  IconButton,
  FormControl,
  MenuItem,
  Select,
  TextField,
  Typography,
  InputAdornment,
  Button,
  Box,
  CircularProgress,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import Image from 'next/image';
import { EMetricType, IMetric } from "@/types/metric";
import { useGeneralStore } from "@/providers/general-store-provider";
import { useDebounce } from "react-use";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import {  IMetricDto } from "@/app/api/types/metricDto";
import useMetricCreationFlowAnalytics from "../../hooks/useMetricCreationFlowAnalytics";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import { MetricTemplate } from "@/hooks/useMetricTemplates";
import Tooltip from "@mui/material/Tooltip";
import MetricsTemplateDialog from '../MetricsTemplateDialog/MetricsTemplateDialog';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useCreateMetricMutation } from "../../api/useCreateMetric";
import { useUpdateMetricMutation } from "../../api/useUpdateMetrics";

interface IMetricModalProps {
  onCancel: () => void;
  isModalOpen: boolean;
  initialData: IMetric | null;
  templateData?: MetricTemplate | null;
  onMetricCreated?: () => void;

}

const MetricsModal = ({
  onCancel,
  isModalOpen,
  initialData,
  templateData,
  onMetricCreated,
}: IMetricModalProps) => {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const metricCreationFlowAnalytics = useMetricCreationFlowAnalytics();
  const notify = useNotification();
  const { addMetric, editMetric, currentAgentId } = useGeneralStore((state) => state);

  // Form state
  const [name, setName] = useState(initialData?.name || "");
  const [metricType, setMetricType] = useState(initialData?.metricType || EMetricType.binary);
  const [metricPrompt, setMetricPrompt] = useState(initialData?.metricPrompt || "");
  const [submittable, setSubmittable] = useState(false);
  const [loading, setLoading] = useState(false);
  const [validationErrors, setValidationErrors] = useState<{ [key: string]: string }>({});
  const [modalOpenedTime, setModalOpenedTime] = useState<number | null>(null);
  const [step, setStep] = useState<'choice' | 'form'>(initialData ? 'form' : 'choice');
  const [selectedOption, setSelectedOption] = useState<'scratch' | 'library' | null>(initialData ? 'scratch' : null);
  const [isExploreMoreOpen, setIsExploreMoreOpen] = useState(false);
  const [selectedTemplate] = useState<MetricTemplate | null>(null);
  const [touched, setTouched] = useState({ name: false, metricType: false, metricPrompt: false });
  const createMetricMutation = useCreateMetricMutation(currentAgentId);
  const editMetricMutation = useUpdateMetricMutation(currentAgentId, initialData?.id ?? '');


  // Prefill for edit
  useEffect(() => {
    if (initialData) {
      setName(initialData.name);
      setMetricType(initialData.metricType.charAt(0).toUpperCase() + initialData.metricType.slice(1) as EMetricType);
      setMetricPrompt(initialData.metricPrompt);
    } else if (!templateData) {
      setName("");
      setMetricType(EMetricType.binary);
      setMetricPrompt("");
    }
  }, [initialData, templateData]);

  // Modal open analytics
  useEffect(() => {
    if (isModalOpen) {
      setModalOpenedTime(Date.now());
      const modalSource = initialData ? "update" : "create";
      metricCreationFlowAnalytics.trackMetricCreationModalOpened(modalSource);
    }
  }, [isModalOpen, initialData, metricCreationFlowAnalytics]);

  // Validation
  useEffect(() => {
    const errors: { [key: string]: string } = {};
    if (!name.trim()) errors.name = "Metric name is required";
    if (!metricType) errors.metricType = "Metric type is required";
    if (!metricPrompt.trim()) errors.metricPrompt = "Metric description is required";
    setValidationErrors(errors);
    setSubmittable(Object.keys(errors).length === 0);
  }, [name, metricType, metricPrompt]);

  // Analytics for validation errors
  useDebounce(
    () => {
      Object.entries(validationErrors).forEach(([field, error]) => {
        if (error) {
          generalAnalyticsEvents.trackFormValidationError("MetricsModal", field, error);
        }
      });
    },
    2000,
    [validationErrors]
  );

  // Cancel handler
  const handleCancel = () => {
    const timeSpent = modalOpenedTime ? Date.now() - modalOpenedTime : 0;
    const fieldsFilled = [name, metricType, metricPrompt].filter(Boolean).length;
    generalAnalyticsEvents.trackModalCloseButtonClicked(
      "metric",
      timeSpent,
      `${fieldsFilled}/3`,
    );
    metricCreationFlowAnalytics.trackMetricCreationAbandoned(
      timeSpent,
      fieldsFilled,
      "cancel",
    );
    onCancel();
  };

  // Submit handler
  const handleSubmit = async () => {
    if (modalOpenedTime) {
      const form_completion_time = Date.now() - modalOpenedTime;
      metricCreationFlowAnalytics.trackMetricCreationSubmitted(
        form_completion_time,
        submittable,
      );
    }
    if (!submittable) return;
    setLoading(true);

    if (initialData) {
      // Edit existing metric
      await editMetricMutation.mutateAsync({
        name,
        prompt: metricPrompt,
        type: metricType.toLowerCase(),
      }, {
        onSuccess: (responseBody: IMetricDto) => {
          editMetric({
            id: String(responseBody.id),
            name: responseBody.name,
            metricPrompt: responseBody.prompt,
            metricType: responseBody.type.toLowerCase() as EMetricType,
          });
          metricCreationFlowAnalytics.trackMetricCreationCompleted(
            String(responseBody.id),
            responseBody.name,
            responseBody.type,
          );
          setLoading(false);
          onCancel();
        },
        onError: (error: any) => {
          setLoading(false);

          notify.error({
            message: "Failed to edit metric",
            description: error.message || "Unknown error occurred",
          });

          generalAnalyticsEvents.trackApiRequestFailed(
            "Edit Metric",
            String(error.status),
            "Failed to edit metric",
          );
          generalAnalyticsEvents.trackApiRequestFailed(
            "Edit Metric",
            "500",
            "Failed to edit metric.",
          );
        },
      });
    } else {

      // Create new metric
      await createMetricMutation.mutateAsync({
        name,
        prompt: metricPrompt,
        type: metricType.toLowerCase(),
      }, {
        onSuccess: (responseBody: IMetricDto) => {
          setName("");
          if (onMetricCreated) {
            onMetricCreated();
          }
          setMetricType(EMetricType.binary);
          setMetricPrompt("");
          setTouched({ name: false, metricType: false, metricPrompt: false });
          setStep('choice');
          setSelectedOption(null);
          addMetric({
            id: String(responseBody.id),
            name: responseBody.name,
            metricPrompt: responseBody.prompt,
            metricType: responseBody.type.toLowerCase() as EMetricType,
          });
          notify.success({
            message: 'Metric created successfully',
            description: 'Metric created successfully',
          });
          setLoading(false);
          onCancel();
        },
        onError: (error: any) => {
          const errorMessage = error.response?.data?.errors;
          if (Array.isArray(errorMessage)) {
            errorMessage.forEach((err: any) => {
              notify.error({
                message: 'Failed to create metric',
                description: `Field: ${err.loc?.join('.') || ''} - ${err.msg}`,
              });
            });
          } else {
            notify.error({
              message: 'Failed to create metric',
              description: error.response?.data?.detail || error.message,
            });
          }
          generalAnalyticsEvents.trackApiRequestFailed(
            "Add Metric",
            String(error.status || '500'),
            "Failed to add metric",
          );
        },
        onSettled: () => {
          setLoading(false);
        },
      });
    }
  };

  const handleExploreMoreOpen = () => {
    setIsExploreMoreOpen(true);
  };

  const handleExploreMoreClose = () => {
    setIsExploreMoreOpen(false);
  };

  useEffect(() => {
    if (isModalOpen) {
      if (initialData) {
        setStep('form');
        setSelectedOption('scratch');
      } else {
        setStep('choice');
        setSelectedOption(null);
      }
    }
  }, [isModalOpen, initialData]);

  useEffect(() => {
    if (isModalOpen) {
      setTouched({ name: false, metricType: false, metricPrompt: false });
      if (!initialData) {
        setMetricType(EMetricType.binary);
      }
    }
  }, [isModalOpen, initialData]);

  const handleBack = () => {
    setStep('choice');
    setSelectedOption(null);
  };

  if (step === 'choice') {
    return (
      <>
        <Dialog
          open={isModalOpen}
          onClose={handleCancel}
          maxWidth="sm"
          fullWidth
          slotProps={{
            paper: {
              sx: { borderRadius: '24px', width: 520, backgroundColor: '#FFFFFF' },
            },
          }}
        >
          <DialogTitle
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 2,
              fontFamily: 'Plus Jakarta Sans',
              fontWeight: 800,
              fontSize: 24,
              pt: 3,
              pb: 2,
            }}
          >
            <Box
              sx={{
                width: 48,
                height: 48,
                borderRadius: '14.67px',
                background: '#fff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                boxShadow: '0px 0px 4.89px rgba(159, 159, 159, 0.25)',
              }}
            >
              <Image src="/task-square-dark.svg" alt="Metric" width={28} height={28} />
            </Box>
            <Typography
              variant="h5"
              sx={{ fontWeight: 700, fontFamily: 'Plus Jakarta Sans', fontSize: 20, color: '#1C1C1C' }}
            >
              Create New Metric
            </Typography>
            <Box flexGrow={1} />
            <IconButton onClick={handleCancel} size="small">
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent sx={{ pt: 1, pb: 3 }}>
            <Box display="flex" flexDirection="column" gap={2}>
              {/* Create from scratch */}
              <Box
                onClick={() => {
                  setSelectedOption('scratch');
                  setStep('form');
                  setMetricType(EMetricType.binary);
                }}
                sx={{
                  border: '1.5px solid #E5E7EB',
                  borderRadius: '20px',
                  background: '#fff',
                  p: 3,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: '#7F56D9',
                    background: '#F9F5FF',
                    '& .scratch-icon-container': {
                      background: '#E9D7FE',
                    },
                    '& .scratch-icon-image': {
                      filter: 'brightness(1) saturate(1)',
                    },
                    '& .scratch-icon-svg path': {
                      stroke: '#7F56D9',
                    }
                  },
                }}
              >
                <Box
                  className="scratch-icon-container"
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '12px',
                    background: '#F7F7F7',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 0.5,
                    transition: 'all 0.2s ease',
                  }}
                >
                  <Box className="scratch-icon-svg" sx={{ width: 22, height: 22 }}>
                    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g id="elements">
                        <path id="Ellipse 438" d="M1 11C1 16.5228 5.47715 21 11 21C16.5228 21 21 16.5228 21 11C21 5.47715 16.5228 0.999999 11 0.999999" stroke="#667085" strokeWidth="1.5" strokeLinecap="round" />
                        <path id="Ellipse 439" d="M3.64856 4.07876C3.7869 3.93211 3.92948 3.7895 4.0761 3.65111M6.94733 1.72939C7.12884 1.6478 7.31313 1.57128 7.5 1.5M1.5 7.5C1.57195 7.31127 1.64925 7.12518 1.73172 6.94192" stroke="#667085" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        <path id="Vector" d="M11 7V15M15 11L7 11" stroke="#667085" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                      </g>
                    </svg>
                  </Box>
                </Box>
                <Box>
                  <Typography sx={{ fontWeight: 600, fontFamily: 'Plus Jakarta Sans', fontSize: 16, color: '#101828' }}>
                    Create Metric from scratch
                  </Typography>
                  <Typography sx={{ fontWeight: 400, fontFamily: 'Plus Jakarta Sans', fontSize: 14, color: '#667085', mt: 0.5 }}>
                    Definition: How well does the support bot stick to the context of the conversation and understand the user&apos;s intent?
                  </Typography>
                </Box>
              </Box>
              {/* Create from metrics library */}
              <Box
                onClick={handleExploreMoreOpen}
                sx={{
                  border: '1.5px solid #E5E7EB',
                  borderRadius: '20px',
                  background: '#fff',
                  p: 3,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 1,
                  cursor: 'pointer',
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    borderColor: '#7F56D9',
                    background: '#F9F5FF',
                    '& .library-icon-container': {
                      background: '#E9D7FE',
                    },
                    '& .library-icon-svg path': {
                      stroke: '#7F56D9',
                    }
                  },
                }}
              >
                <Box
                  className="library-icon-container"
                  sx={{
                    width: 40,
                    height: 40,
                    borderRadius: '12px',
                    background: '#F7F7F7',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mb: 0.5,
                    transition: 'all 0.2s ease',
                  }}
                >
                  <Box className="library-icon-svg" sx={{ width: 22, height: 22 }}>
                    <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <g id="elements">
                        <path id="Vector" d="M11.3994 7.29796L14.3193 6.32465C15.2056 6.02924 15.6487 5.88153 15.8826 6.11544C16.1165 6.34935 15.9688 6.79247 15.6734 7.67871L14.7001 10.5986C14.1967 12.1088 13.945 12.8639 13.4035 13.4054C12.862 13.9469 12.1069 14.1986 10.5967 14.702L7.67676 15.6753C6.79052 15.9708 6.34739 16.1185 6.11349 15.8846C5.87958 15.6507 6.02729 15.2075 6.3227 14.3213L7.29601 11.4014C7.79941 9.89118 8.0511 9.13608 8.59262 8.59457C9.13413 8.05306 9.88922 7.80136 11.3994 7.29796Z" stroke="#667085" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                        <path id="Vector_2" d="M10.9976 11L10.9912 11.0064" stroke="#667085" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                        <path id="Rectangle 116" d="M1.49805 11C1.49805 6.52166 1.49805 4.28249 2.88929 2.89124C4.28053 1.5 6.5197 1.5 10.998 1.5C15.4764 1.5 17.7156 1.5 19.1068 2.89124C20.498 4.28249 20.498 6.52166 20.498 11C20.498 15.4783 20.498 17.7175 19.1068 19.1088C17.7156 20.5 15.4764 20.5 10.998 20.5C6.5197 20.5 4.28053 20.5 2.88929 19.1088C1.49805 17.7175 1.49805 15.4783 1.49805 11Z" stroke="#667085" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                      </g>
                    </svg>
                  </Box>
                </Box>
                <Box>
                  <Typography sx={{ fontWeight: 600, fontFamily: 'Plus Jakarta Sans', fontSize: 16, color: '#101828' }}>
                    Metric Library
                  </Typography>
                  <Typography sx={{ fontWeight: 400, fontFamily: 'Plus Jakarta Sans', fontSize: 14, color: '#667085', mt: 0.5 }}>
                    Definition: How well does the support bot stick to the context of the conversation and understand the user&apos;s intent?
                  </Typography>
                </Box>
              </Box>
            </Box>
          </DialogContent>
        </Dialog>
        <MetricsTemplateDialog
          open={isExploreMoreOpen}
          onClose={handleExploreMoreClose}
        />
      </>
    );
  }

  return (
    <Dialog
      open={isModalOpen}
      onClose={handleCancel}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: "28px", width: 565, backgroundColor: "#FFFFFF" },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          p: 2,
          borderBottom: "none",
        }}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <Box
            sx={{
              backgroundColor: "#fff",
              borderRadius: "15px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              padding: "12px",
              boxShadow: "0px 0px 4.89px rgba(159, 159, 159, 0.25)",
              transition: "all 0.3s ease",
            }}
          >
            <Image src="task-square-dark.svg" alt="Metric" width={25} height={25} />
          </Box>
          <Typography
            variant="h5"
            sx={{ fontWeight: 700, fontFamily: "Plus Jakarta Sans" }}
          >
            {initialData ? "Update Metric" : "Create new metric"}
          </Typography>
        </Box>
        <Box>
          <IconButton
            aria-label="close"
            onClick={handleCancel}
            size="small"
            sx={{ color: "text.secondary" }}
          >
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>
      <DialogContent sx={{ margin: '0 24px', padding: 0 }}>
        <Box component="form" sx={{ mt: 1 }}>
          {/* Name */}
          <FormControl fullWidth sx={{ mb: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
              >
                Metric Name*
              </Typography>
            </Box>
            <TextField
              required
              value={name}
              placeholder="Add Name"
              autoComplete="off"
              onChange={e => setName(e.target.value)}
              onBlur={e => { setTouched(t => ({ ...t, name: true })); metricCreationFlowAnalytics.trackMetricNameEntered(e.target.value); }}
              error={!!validationErrors.name && touched.name}
              helperText={touched.name && validationErrors.name ? validationErrors.name : ''}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <Box sx={{
                      width: 28,
                      height: 28,
                      borderRadius: '50%',
                      background: '#F6F6F6',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      mr: 1,
                    }}>
                      <Image src="/grammerly.svg" alt="Metric Name" width={18} height={18} />
                    </Box>
                  </InputAdornment>
                ),
              }}
              sx={{
                fontFamily: "Plus Jakarta Sans",
                borderRadius: '16px',
                '& .MuiOutlinedInput-root': {
                  borderRadius: '16px',
                  fontSize: 18,
                  fontWeight: 500,
                  '& input::placeholder': {
                    color: '#BDBDBD',
                    opacity: 1,
                  },
                  '&:hover .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#7F56D9',
                  },
                  '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#7F56D9',
                  },
                },
              }}
            />
          </FormControl>
          {/* Type */}
          <FormControl fullWidth sx={{ mb: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
              >
                Metrics Type
              </Typography>
              <Tooltip
                sx={{
                  fontFamily: "Plus Jakarta Sans",
                  borderRadius: '16px',
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '16px',
                    fontSize: 18,
                    fontWeight: 500,
                    '& input::placeholder': {
                      color: '#BDBDBD',
                      opacity: 1,
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#7F56D9',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#7F56D9',
                    },
                  },
                }}
                title="Type of metric"
                placement="right"
                onOpen={() =>
                  generalAnalyticsEvents.trackHelpIconClicked(
                    "MetricsModal",
                    "Metric Type"
                  )
                }
              >
                <Box sx={{ ml: 1, display: 'flex', alignItems: 'center' }}>
                  <Image src="/tooltip-icon.svg" alt="Tooltip" width={18} height={18} />
                </Box>
              </Tooltip>
            </Box>
            <Select
              value={metricType}
              onChange={e => {
                setMetricType(e.target.value as EMetricType);
                setTouched(t => ({ ...t, metricType: true }));
                metricCreationFlowAnalytics.trackMetricTypeSelected(e.target.value, 0);
              }}
              onBlur={() => setTouched(t => ({ ...t, metricType: true }))}
              displayEmpty
              required
              error={!!validationErrors.metricType && touched.metricType}
              sx={{
                fontFamily: "Plus Jakarta Sans",
                borderRadius: '16px',
                fontSize: 14,
                fontWeight: 600,
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#7F56D9',
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#E0E0E0',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: '#7F56D9',
                },


              }}
              renderValue={selected => (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Box sx={{
                    width: 28,
                    height: 28,
                    borderRadius: '50%',
                    background: '#F6F6F6',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mr: 1.5,
                  }}>
                    <Image src="/programming-arrow.svg" alt="Metrics Type" width={18} height={18} />
                  </Box>
                  <Typography sx={{ fontWeight: 600, fontSize: 14, color: '#101828', fontFamily: 'Plus Jakarta Sans' }}>{selected}</Typography>
                </Box>
              )}
            >
              <MenuItem value="" disabled sx={{ fontSize: 14, fontWeight: 600, fontFamily: 'Plus Jakarta Sans' }}>
                Select
              </MenuItem>
              {Object.values(EMetricType).map(type => (
                <MenuItem key={type} value={type} sx={{ fontSize: 14, fontWeight: 600, fontFamily: 'Plus Jakarta Sans' }}>{type}</MenuItem>
              ))}
            </Select>
            {touched.metricType && validationErrors.metricType ? (
              <Typography variant="caption" color="error" sx={{ fontFamily: "Plus Jakarta Sans" }}>
                {validationErrors.metricType}
              </Typography>
            ) : null}
          </FormControl>
          {/* Prompt */}
          <FormControl fullWidth sx={{ mb: 2 }}>
            <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
              <Typography
                variant="subtitle1"
                sx={{ fontWeight: 500, fontFamily: "Plus Jakarta Sans" }}
              >
                Metric Description*
              </Typography>
              <Tooltip
                sx={{ fontFamily: "Plus Jakarta Sans" }}
                title="This prompt will be used to evaluate the metric"
                placement="right"
                onOpen={() =>
                  generalAnalyticsEvents.trackHelpIconClicked(
                    "MetricsModal",
                    "Metric Prompt"
                  )
                }
              >
                <Box sx={{ ml: 1, display: 'flex', alignItems: 'center' }}>
                  <Image src="/tooltip-icon.svg" alt="Tooltip" width={18} height={18} />
                </Box>
              </Tooltip>
            </Box>
            <TextField
              required
              multiline
              autoComplete="off"
              placeholder="How well does the support bot stick to the context of the conversation and understand the user's intent?"
              rows={6}
              value={selectedOption === 'library' && selectedTemplate ? selectedTemplate.description : metricPrompt}
              onChange={e => setMetricPrompt(e.target.value)}
              onBlur={e => { setTouched(t => ({ ...t, metricPrompt: true })); metricCreationFlowAnalytics.trackMetricDescriptionEntered(e.target.value); }}
              error={!!validationErrors.metricPrompt && touched.metricPrompt}
              helperText={touched.metricPrompt && validationErrors.metricPrompt ? validationErrors.metricPrompt : ''}
              sx={{
                fontFamily: "Plus Jakarta Sans",
                "& .MuiOutlinedInput-root": {
                  borderRadius: "12px",
                  "&:hover .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#7F56D9",
                  },
                  "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                    borderColor: "#7F56D9",
                  },
                },
              }}
            />
          </FormControl>
        </Box>
      </DialogContent>
      <DialogActions sx={{ margin: '0px 16px 16px 24px', borderTop: 'none', display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 3 }}>
        <Button
          onClick={handleBack}
          variant="text"
          startIcon={<ArrowBackIcon sx={{ fontSize: 24, color: '#444' }} />}
          sx={{
            background: '#F6F6F6',
            borderRadius: '16px',
            fontWeight: 500,
            fontSize: 16,
            color: '#595959',
            height: '64px',
            px: 5,
            boxShadow: 'none',
            textTransform: 'none',
            fontFamily: "Plus Jakarta Sans",
            minWidth: 0,
            flex: 1,
            mr: 0,
            '&:hover': { background: '#F0F0F2' },
          }}
        >
          Go back
        </Button>
        <Button
          onClick={handleSubmit}
          variant="contained"
          sx={{
            backgroundColor: '#8B5CF6',
            borderRadius: '16px',
            fontWeight: 600,
            fontSize: 16,
            color: '#fff',
            height: '64px',
            px: 5,
            boxShadow: 'none',
            fontFamily: "Plus Jakarta Sans",
            textTransform: 'none',
            minWidth: 0,
            flex: 1,
            '&:hover': { backgroundColor: '#7C3AED' },
          }}
          disabled={!submittable || loading}
        >
          {loading ? <CircularProgress size={22} sx={{ color: '#fff' }} /> : (initialData ? 'Update Metric' : 'Create Metric')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default MetricsModal;

import React, { useEffect, useRef, useState, useCallback } from "react";
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  FormControl,
  Select as MUISelect,
  MenuItem,
  TextField,
  Tooltip,
  Typography,
  Button,
  Box,
  Stack,
  Chip,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import SwapHorizIcon from '@mui/icons-material/SwapHoriz';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import Menu from '@mui/material/Menu';
import { useGeneralStore } from "@/providers/general-store-provider";
import { useAuthStore } from "@/stores/auth-store";
import useRudderStackAnalytics from "@/hooks/useRudderAnalytics";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import { usePathname } from "next/navigation";
import { IErrorFields } from "@/types/validateError";
import { useDebounce } from "react-use";
import SimulationSvg from "../../../../../public/side-nav/magic-wand-01.svg";
import RocketLaunchIcon from '@mui/icons-material/RocketLaunch';
import { useOnboardingStore } from '@/stores/onboarding-store';
import Image from 'next/image';
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import { renderMetricsSelect } from "../MetricsSelectButton/MetricsSelectButton";

interface IGenerateScenariosModalProps {
  onCancel: () => void;
  isModalOpen: boolean;
  onCustomScenario?: () => void;
}

// Custom dropdown icon component
const CustomDropdownIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    data-testid="ArrowDropDownIcon"
    style={{
      position: 'absolute',
      right: '14px',
      top: '50%',
      transform: 'translateY(-50%)',
      pointerEvents: 'none'
    }}
  >
    <path
      d="M18 9.00005C18 9.00005 13.5811 15 12 15C10.4188 15 6 9 6 9"
      stroke="#141B34"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const GenerateScenariosModal = ({
  onCancel,
  isModalOpen,
  onCustomScenario,
}: IGenerateScenariosModalProps) => {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const pathname = usePathname();
  const analytics = useRudderStackAnalytics();
  const { metrics, currentAgentId, setMetrics } = useGeneralStore((state) => state);
  const { user } = useAuthStore((state) => state);
  const { setStepComplete, nextStep } = useOnboardingStore();
  const notify = useNotification();

  const [isCreating, setIsCreating] = useState(false);
  const [submittable, setSubmittable] = useState<boolean>(false);
  const [modalOpenedTime, setModalOpenedTime] = useState<number | null>(null);
  const [validationErrors] = useState<IErrorFields[]>([]);
  const isCreatedRef = useRef<boolean>(false);
  const [step, setStep] = useState<'choice' | 'details' | 'personality'>('choice');

  // MUI controlled fields
  const [numberOfScenarios, setNumberOfScenarios] = useState(1);
  const [instructions, setInstructions] = useState("");
  const [personality, setPersonality] = useState("American Male");
  const [selectedMetrics, setSelectedMetrics] = useState<string[]>([]);

  // Add state for menu anchor
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleMenuClose = () => {
    setAnchorEl(null);
  };
  const handleTemplateSelect = (template: string) => {
    fillInstructions(template);
    handleMenuClose();
  };

  // Sync AntD validation logic with MUI fields
  useEffect(() => {
    // Simulate AntD validation
    if (numberOfScenarios < 1 || !personality || selectedMetrics.length === 0) {
      setSubmittable(false);
    } else {
      setSubmittable(true);
    }
  }, [numberOfScenarios, personality, selectedMetrics]);

  useEffect(() => {
    if (isModalOpen) {
      setModalOpenedTime(Date.now());
      analytics?.track("Test_Creation_Started", {
        category: "Simulation",
        email: user?.email,
        domain: user?.email?.split("@")[1],
        user_id: user?.id,
        app: "test.ai",
        entry_point: "imported",
        page_name: pathname,
      });
    }
  }, [isModalOpen, analytics, pathname, user?.email, user?.id]);

  useDebounce(
    () => {
      validationErrors.forEach((fieldError: IErrorFields) => {
        // No direct MUI equivalent for isFieldTouched, so skip
        generalAnalyticsEvents.trackFormValidationError(
          "GenerateScenariosModal",
          fieldError.name[0],
          fieldError.errors[0],
        );
      });
    },
    2000,
    [validationErrors],
  );

  // Add fetchMetrics function
  const fetchMetrics = useCallback(async () => {
    try {
      const response = await fetch(`/api/agents/${currentAgentId}/metrics`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${user?.token || ""}`,
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          'Pragma': 'no-cache',
          'Expires': '0'
        },
      });

      if (!response.ok) {
        console.error(`HTTP error! status: ${response.status}`);
        notify.error({
          message: "Failed to get metrics",
          description: `HTTP error! status: ${response.status}`,
        });
        generalAnalyticsEvents.trackApiRequestFailed(
          "Get Metrics",
          String(response.status),
          `Failed to get metrics`,
        );
        return;
      }

      const responseBody = await response.json();
      const formattedMetrics = responseBody.map((metric: any) => ({
        id: String(metric.id),
        metricType: metric.type,
        metricPrompt: metric.prompt,
        name: metric.name,
      }));

      setMetrics(formattedMetrics);
    } catch (error) {
      console.error("Error fetching metrics:", error);
      notify.error({
        message: "Unexpected Error",
        description: "Failed to load metrics.",
      });
      generalAnalyticsEvents.trackApiRequestFailed(
        "Get Metrics",
        "500",
        `Failed to load metrics`,
      );
    }
  }, [currentAgentId, user?.token, notify, generalAnalyticsEvents, setMetrics]);

  // // Fetch metrics when switching to personality step
  // useEffect(() => {
  //   if (step === 'personality') {
  //     fetchMetrics();
  //   }
  // }, [step, fetchMetrics]);

  // Handlers
  const handleCancel = () => {
    if (!isCreatedRef.current && !isCreating) {
      analytics?.track("Test_Creation_Abandoned", {
        category: "Simulation",
        email: user?.email,
        domain: user?.email.split("@")[1],
        user_id: user?.id,
        app: "test.ai",
        page_name: pathname,
      });
    }
    const timeSpent = modalOpenedTime ? Date.now() - modalOpenedTime : 0;
    generalAnalyticsEvents.trackModalCloseButtonClicked(
      "GenerateScenariosModal",
      timeSpent,
      `${[numberOfScenarios, personality, selectedMetrics, instructions].filter(Boolean).length}/4`,
    );
    onCancel();
  };

  const handleCreate = () => {
    isCreatedRef.current = true;
    setIsCreating(true);
    try {
      analytics?.track("Generate Scenarios", {
        category: "Simulation",
        email: user?.email,
        domain: user?.email.split("@")[1],
        userId: user?.id,
        app: "test.ai",
        page_name: pathname,
      });
      const request_body = {
        personality,
        metrics: selectedMetrics.map((metric: string) => Number(metric)),
        number_of_scenarios: numberOfScenarios,
        bot_settings: instructions,
        instructions: instructions,
      };
      const ws = new WebSocket(
        `${process.env.NEXT_PUBLIC_BASE_URL_SCENARIOS?.replace(
          "https",
          "wss",
        )}/agents/${currentAgentId}/generate_scenarios?token=` + user?.token,
      );
      ws.onopen = () => {
        ws.send(JSON.stringify(request_body));
      };
      ws.onmessage = () => {
        // Mark second step as complete and move to next step
        setStepComplete('createScenario');
        nextStep();
        handleCancel();
      };
      ws.onerror = (error) => {
        console.error("WebSocket error:", error);
        notify.error({
          message: "Failed to generate scenarios",
          description: "Please try again.",
        });
        setIsCreating(false);
        handleCancel();
      };
      ws.onclose = () => {
        setIsCreating(false);
        analytics?.track("Test_Creation_Completed", {
          category: "Simulation",
          email: user?.email,
          domain: user?.email.split("@")[1],
          user_id: user?.id,
          app: "test.ai",
          test_type: "generated",
          page_name: pathname,
        });
        handleCancel();
      };
    } catch (e) {
      console.error(e);
      setIsCreating(false);
      handleCancel();
    }
  };

  const fillInstructions = (text: string) => {
    setInstructions(text);
  };

  const handleCustomScenario = () => {
    if (onCustomScenario) onCustomScenario();
    onCancel();
  };

  // Add templates
  const templates = [
    {
      title: "Real estate enquiry",
      description: "You are a UAE resident looking for apartments in Abu Dhabi"
    },
    {
      title: "Hospital appointment",
      description: "You are an AI agent that takes appointment for hospitals based on the patient's needs"
    },
    {
      title: "Travel inquiry",
      description: "You are planning a trip to Europe and need recommendations for flights and hotels"
    },
    {
      title: "Customer support",
      description: "You are an AI agent specialized in handling customer support queries regarding product returns and technical issues"
    }
  ];

  // Define the steps for the indicator (3 steps in total)
  const steps = ['choice', 'details', 'personality'];
  const stepIndex = steps.indexOf(step);

  // Add new state for MetricsModal
  const [isMetricsModalOpen, setIsMetricsModalOpen] = useState(false);

  // Helper: Metrics select or create button


  // Step 1: Choice (Create from scratch)
  if (step === 'choice') {
    return (
      <Dialog
        open={isModalOpen}
        onClose={handleCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '24px', width: 540, backgroundColor: '#FFFFFF' },
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 800,
            fontSize: 24,
            pt: 3,
            pb: 2,
          }}
        >
          <Box
            sx={{
              width: 44,
              height: 44,
              borderRadius: '14.5px',
              background: '#fff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 2px 8px 0 rgba(16, 24, 40, 0.10)',
            }}
          >
            {/* Book icon or similar, use MUI or custom SVG */}

            {/* Replace with your icon if needed */}
            <Image src="/template-create-new-scenario.svg" alt="Create Scenario" width={20} height={19}  />
          </Box>
          <Typography
            sx={{ fontWeight: 700, fontFamily: 'Plus Jakarta Sans', fontSize: 20, color: '#1C1C1C' }}
          >
            Create New Scenario
          </Typography>
          <Box flexGrow={1} />
          <IconButton onClick={handleCancel} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: 2, pb: 3, mt: 1 }}>
          <Box display="flex" flexDirection="column" gap={2}>
            {/* Create from scratch card */}
            <Box
              onClick={() => setStep('details')}
              sx={{
                border: '2px solid #B692F6',
                borderRadius: '20px',
                background: '#fff',
                p: 2,
                gap: 3,
                cursor: 'pointer',
                boxShadow: '0 0 0 2px #F4EBFF',
                transition: 'border 0.2s, box-shadow 0.2s, background 0.2s',
                '&:hover': {
                  borderColor: '#7F56D9',
                  background: '#F9F5FF',
                },
              }}
            >
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: '16px',
                  background: '#F4EBFF',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  mb: '5px',
                }}
              >
                <Image src="/add circle-half-dot.svg" alt="Add" width={24} height={24} />
              </Box>
              <Box>
                <Typography sx={{ fontWeight: 600, fontFamily: 'Plus Jakarta Sans', fontSize: 16, color: '#101828' }}>
                  Create Scenario from scratch
                </Typography>
                <Typography sx={{ fontWeight: 400, fontFamily: 'Plus Jakarta Sans', fontSize: 14, color: '#667085' }}>
                  Definition: How well does the support bot stick to the context of the conversation and understand the user&apos;s intent?
                </Typography>
              </Box>
            </Box>
          </Box>
        </DialogContent>
      </Dialog>
    );
  }

  // Step 2: Details (Instructions & Number of Runs)
  if (step === 'details') {
    const maxInstructionsLength = 1000;
    const creditsRemaining = 9652; // Placeholder, replace with real value if available
    const onlyWhitespace = instructions.length > 0 && instructions.trim().length === 0;
    const startsWithWhitespace = instructions.length > 0 && /^\s/.test(instructions);
    const invalidInstructions = onlyWhitespace || startsWithWhitespace;
    const detailsValid = !invalidInstructions && instructions.length > 0 && numberOfScenarios > 0;
    return (
      <Dialog
        open={isModalOpen}
        onClose={handleCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '24px', width: 520, backgroundColor: '#FFFFFF' },
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 800,
            fontSize: 24,
            pt: 3,
            pb: 2,
          }}
        >
          <Box
            sx={{
              width: 48,
              height: 48,
              borderRadius: '16px',
              background: '#fff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 2px 8px 0 rgba(16, 24, 40, 0.10)',
            }}
          >
            {/* Magic wand/star icon */}
            <Image src="/add circle-half-dark.svg" alt="Create Scenario" width={24} height={24} />
          </Box>
          <Typography
            variant="h5"
            sx={{ fontWeight: 700, fontFamily: 'Plus Jakarta Sans', fontSize: 20, color: '#1C1C1C' }}
          >
            Create Scenarios
          </Typography>
          <Box flexGrow={1} />
          <IconButton onClick={handleCancel} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 0 }}>
          <Box display="flex" flexDirection="column" gap={3}>
            {/* Number of Scenarios */}
            <Box width="100%">
              <Typography sx={{ fontWeight: 600, fontFamily: 'Plus Jakarta Sans', fontSize: 16, mb: 1, color: '#101828' }}>
                Number of Scenario
                <Tooltip title="Amount of scenarios to generate. The more scenarios, the longer it will take to generate them." placement="right">
                  <Image src="/tooltip-icon.svg" alt="Tooltip" width={18} height={18} style={{ marginLeft: 4 }} />
                </Tooltip>
              </Typography>
              <Box display="flex" alignItems="center" gap={2} width="100%">
                <Button
                  variant="outlined"
                  onClick={() => setNumberOfScenarios(Math.max(1, numberOfScenarios - 1))}
                  sx={{
                    minWidth: 0,
                    width: 48,
                    height: 48,
                    borderRadius: '16px',
                    border: '1.5px solid #E5E7EB',
                    color: '#7F56D9',
                    fontSize: 28,
                    fontWeight: 700,
                    background: '#fff',
                    boxShadow: 'none',
                    '&:hover': { background: '#F9F5FF', borderColor: '#7F56D9' },
                  }}
                >
                  –
                </Button>
                <Box
                  sx={{
                    flex: 1,
                    height: 48,
                    borderRadius: '16px',
                    border: '1.5px solid #E5E7EB',
                    background: '#F6F6F6',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontFamily: 'Plus Jakarta Sans',
                    fontWeight: 700,
                    fontSize: 24,
                    color: '#7F56D9',
                    letterSpacing: '0.1em',
                  }}
                >
                  {numberOfScenarios.toString().padStart(2, '0')}
                </Box>
                <Button
                  variant="outlined"
                  onClick={() => setNumberOfScenarios(Math.min(99, numberOfScenarios + 1))}
                  sx={{
                    minWidth: 0,
                    width: 48,
                    height: 48,
                    borderRadius: '16px',
                    border: '1.5px solid #E5E7EB',
                    color: '#7F56D9',
                    fontSize: 28,
                    fontWeight: 700,
                    background: '#fff',
                    boxShadow: 'none',
                    '&:hover': { background: '#F9F5FF', borderColor: '#7F56D9' },
                  }}
                >
                  +
                </Button>
              </Box>
            </Box>
            {/* Instructions */}
            <Box width="100%">
              <Box display="flex" alignItems="center" mb={1}>
                <Typography sx={{ fontWeight: 600, fontFamily: 'Plus Jakarta Sans', fontSize: 16, color: '#101828' }}>
                  Instructions
                  <Tooltip title="Instructions for the scenarios, this will be used by the AI to generate the scenarios." placement="right">
                    <Image src="/tooltip-icon.svg" alt="Tooltip" width={18} height={18} style={{ marginLeft: 4 }} />
                  </Tooltip>
                </Typography>
              </Box>
              <TextField
                multiline
                rows={5}
                value={instructions}
                onChange={e => setInstructions(e.target.value.slice(0, maxInstructionsLength))}
                placeholder="Definition: How well does the support bot stick to the context of the conversation and understand the user&apos;s intent?"
                fullWidth
                sx={{
                  fontFamily: 'Plus Jakarta Sans',
                  borderRadius: '16px',
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '16px',
                    fontSize: 16,
                    fontWeight: 500,
                    background: '#F6F6F6',
                    '& input::placeholder': {
                      color: '#BDBDBD',
                      opacity: 1,
                    },
                    '&:hover .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#7F56D9',
                    },
                    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                      borderColor: '#7F56D9',
                    },
                  },
                }}
                inputProps={{ maxLength: maxInstructionsLength }}
                error={invalidInstructions}
                helperText={
                  onlyWhitespace
                    ? "Instructions cannot be only whitespace."
                    : startsWithWhitespace
                      ? "Instructions cannot start with whitespace."
                      : ""
                }
              />
              <Box display="flex" justifyContent="space-between" alignItems="center" mt={1}>
                <Typography sx={{ fontFamily: 'Plus Jakarta Sans', fontSize: 14, color: '#BDBDBD' }}>
                  {instructions.length}/{maxInstructionsLength}
                </Typography>
                <Box display="flex" alignItems="center" gap={1}>
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="10" cy="10" r="10" fill="#F4EBFF" />
                    <path d="M10 5V10L13 12" stroke="#7F56D9" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  <Typography sx={{ fontFamily: 'Plus Jakarta Sans', fontSize: 14, color: '#7F56D9', fontWeight: 600 }}>
                    {creditsRemaining.toLocaleString()} credits remaining
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3, pt: 2, borderTop: 'none', display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 3 }}>
          <Button
            onClick={() => setStep('choice')}
            variant="text"
            startIcon={<ArrowBackIcon sx={{ fontSize: 20, color: '#595959' }} />}
            sx={{
              background: '#F6F6F6',
              borderRadius: '16px',
              fontWeight: 500,
              fontSize: 16,
              color: '#595959',
              height: '56px',
              px: 5,
              boxShadow: 'none',
              textTransform: 'none',
              fontFamily: 'Plus Jakarta Sans',
              minWidth: 0,
              flex: 1,
              mr: 0,
              '&:hover': { background: '#F0F0F2' },
            }}
          >
            Go back
          </Button>
          <Button
            onClick={() => setStep('personality')}
            variant="contained"
            sx={{
              backgroundColor: '#8B5CF6',
              borderRadius: '16px',
              fontWeight: 600,
              fontSize: 16,
              color: '#fff',
              height: '56px',
              px: 5,
              boxShadow: 'none',
              fontFamily: 'Plus Jakarta Sans',
              textTransform: 'none',
              minWidth: 0,
              flex: 1,
              '&:hover': { backgroundColor: '#7C3AED' },
            }}
            disabled={!detailsValid}
          >
            Next
          </Button>
        </DialogActions>
        {/* Step indicator (3 sections, only shown in details and personality steps) */}
        <Box width="100%" display="flex" alignItems="center" justifyContent="center" sx={{ mt: 1, mb: 3, px: 3 }}>
          <Box display="flex" width="100%" height={8} borderRadius={3} overflow="hidden">
            {steps.map((s, idx) => (
              <Box
                key={s}
                flex={1}
                sx={{
                  background: idx <= stepIndex ? '#7F56D9' : '#F4EBFF',
                  transition: 'background 0.3s',
                  borderRadius: '8px',
                  height: '8px',
                  mr: idx < steps.length - 1 ? 1 : 0,
                }}
              />
            ))}
          </Box>
        </Box>
      </Dialog>
    );
  }

  // Step 3: Personality & Metrics
  if (step === 'personality') {
    const personalityValid = !!personality && selectedMetrics.length > 0;
    return (
      <Dialog
        open={isModalOpen}
        onClose={handleCancel}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: { borderRadius: '24px', width: 520, backgroundColor: '#FFFFFF' },
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 2,
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 800,
            fontSize: 24,
            pt: 3,
            pb: 2,
          }}
        >
          <Box
            sx={{
              width: 48,
              height: 48,
              borderRadius: '16px',
              background: '#fff',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              boxShadow: '0 2px 8px 0 rgba(16, 24, 40, 0.10)',
            }}
          >
            {/* Personality icon */}
            <Image src="/ai-voice-dark.svg" alt="Create Scenario" width={24} height={24} />

          </Box>
          <Typography
            variant="h5"
            sx={{ fontWeight: 700, fontFamily: 'Plus Jakarta Sans', fontSize: 20, color: '#1C1C1C' }}
          >
            Select Personality & Metrics
          </Typography>
          <Box flexGrow={1} />
          <IconButton onClick={handleCancel} size="small">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: 3, pb: 0 }}>
          <Box display="flex" flexDirection="column" gap={3}>
            {/* Select Personality */}
            <Box width="100%">
              <Typography sx={{ fontWeight: 600, fontFamily: 'Plus Jakarta Sans', fontSize: 16, mb: 1, color: '#101828' }}>
                Select Personality
                <Tooltip title="Select the personalities that the AI will use." placement="right">
                  <Image src="/tooltip-icon.svg" alt="Tooltip" width={18} height={18} style={{ marginLeft: 4 }} />
                </Tooltip>
              </Typography>
              <Box sx={{ position: 'relative' }}>
                <MUISelect
                  value={personality}
                  onChange={e => setPersonality(e.target.value)}
                  required
                  displayEmpty
                  fullWidth
                sx={{
                  fontFamily: 'Plus Jakarta Sans',
                  borderRadius: '16px',
                  fontSize: 16,
                  fontWeight: 600,
                  background: '#FFFFFF',
                  border: '1px solid #E0E0E0',
                  '& .MuiOutlinedInput-root': {
                    borderRadius: '16px',
                  },
                  '& .MuiOutlinedInput-notchedOutline': {
                    borderColor: '#E0E0E0',
                    borderWidth: '1px',
                  },
                  '& .MuiSelect-icon': {
                    display: 'none', // Hide the default icon
                  },
                }}
              >
                <MenuItem value="" disabled>Select personality</MenuItem>
                <MenuItem value="American Male">American Male</MenuItem>
                {/* Add more personalities as needed */}
              </MUISelect>
                <CustomDropdownIcon />
              </Box>
            </Box>
            {/* Select Metrics */}
            <Box width="100%">
              <Typography sx={{ fontWeight: 600, fontFamily: 'Plus Jakarta Sans', fontSize: 16, mb: 1, color: '#101828' }}>
                Select Metrics
                <Tooltip title="Metrics help you measure AI performance — track accuracy, response time, and key factors." placement="right">
                  <Image src="/tooltip-icon.svg" alt="Tooltip" width={18} height={18} style={{ marginLeft: 4 }} />
                </Tooltip>
              </Typography>
              {renderMetricsSelect({ metrics, selectedMetrics, setSelectedMetrics, setIsMetricsModalOpen, isMetricsModalOpen, fetchMetrics, MUISelect, Chip, CustomDropdownIcon })}
            </Box>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, pb: 3, pt: 2, borderTop: 'none', display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: 3 }}>
          <Button
            onClick={() => setStep('details')}
            variant="text"
            startIcon={<ArrowBackIcon sx={{ fontSize: 20, color: '#595959' }} />}
            sx={{
              background: '#F6F6F6',
              borderRadius: '16px',
              fontWeight: 500,
              fontSize: 16,
              color: '#595959',
              height: '56px',
              px: 5,
              boxShadow: 'none',
              textTransform: 'none',
              fontFamily: 'Plus Jakarta Sans',
              minWidth: 0,
              flex: 1,
              mr: 0,
              '&:hover': { background: '#F0F0F2' },
            }}
          >
            Go back
          </Button>
          <Button
            onClick={handleCreate}
            variant="contained"
            sx={{
              backgroundColor: '#8B5CF6',
              borderRadius: '16px',
              fontWeight: 600,
              fontSize: 16,
              color: '#fff',
              height: '56px',
              px: 5,
              boxShadow: 'none',
              fontFamily: 'Plus Jakarta Sans',
              textTransform: 'none',
              minWidth: 0,
              flex: 1,
              '&:hover': { backgroundColor: '#7C3AED' },
            }}
            disabled={!personalityValid}
          >
            {isCreating ? 'Processing...' : 'Create Scenario'}
          </Button>
        </DialogActions>
        {/* Step indicator (3 sections, only shown in details and personality steps) */}
        <Box width="100%" display="flex" alignItems="center" justifyContent="center" sx={{ mt: 1, mb: 3, px: 3 }}>
          <Box display="flex" width="100%" height={8} borderRadius={3} overflow="hidden">
            {steps.map((s, idx) => (
              <Box
                key={s}
                flex={1}
                sx={{
                  background: idx <= stepIndex ? '#7F56D9' : '#F4EBFF',
                  transition: 'background 0.3s',
                  height: '8px',
                  borderRadius: '8px',
                  mr: idx < steps.length - 1 ? 1 : 0,
                }}
              />
            ))}
          </Box>
        </Box>
      </Dialog>
    );
  }

  return (
    <Dialog
      open={isModalOpen}
      onClose={handleCancel}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: "16px", width: 565, backgroundColor: "#FFFFFF" },
      }}
    >
      <DialogTitle
        sx={{
          display: "flex",
          justifyContent: "space-between",
          alignItems: "center",
          p: 3,
          pb: 0,
        }}
      >
        <Box display="flex" alignItems="center" gap={1}>
          <Box
            sx={{
              backgroundColor: "#fff",
              borderRadius: "15px",
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              padding: "12px",
              boxShadow: "0px 0px 4.89px rgba(159, 159, 159, 0.25)",
              transition: "all 0.3s ease",
            }}
          >
            <SimulationSvg style={{ width: 25, height: 25 }} />
          </Box>
          <Typography
            variant="h5"
            sx={{ fontWeight: 700, fontFamily: "Plus Jakarta Sans" }}
          >
            Create Scenarios
          </Typography>
        </Box>
        <IconButton aria-label="close" onClick={handleCancel} size="small" sx={{ color: "text.secondary" }}>
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <Box display="flex" justifyContent="flex-end" px={3} mt={1} mb={1}>
        <Button
          variant="outlined"
          startIcon={<SwapHorizIcon />}
          onClick={handleCustomScenario}
          sx={{
            fontFamily: "Plus Jakarta Sans",
            textTransform: "none",
            fontSize: "14px",
            color: "#7F56D9",
            borderColor: "#7F56D9",
            '&:hover': {
              borderColor: "#6941C6",
              backgroundColor: "#F9F5FF"
            }
          }}
        >
          Custom Scenario
        </Button>
      </Box>
      {/* Step indicator (3 sections, only shown in details and personality steps) */}
      <Box width="100%" display="flex" alignItems="center" justifyContent="center" sx={{ mt: 1, mb: 3 }}>
        <Box display="flex" width="100%" maxWidth={360} height={6} borderRadius={3} overflow="hidden">
          {steps.map((s, idx) => (
            <Box
              key={s}
              flex={1}
              sx={{
                background: idx <= stepIndex ? '#7F56D9' : '#F4EBFF',
                transition: 'background 0.3s',
                borderTopLeftRadius: idx === 0 ? 3 : 0,
                borderBottomLeftRadius: idx === 0 ? 3 : 0,
                borderTopRightRadius: idx === steps.length - 1 ? 3 : 0,
                borderBottomRightRadius: idx === steps.length - 1 ? 3 : 0,
                mr: idx < steps.length - 1 ? 1 : 0,
              }}
            />
          ))}
        </Box>
      </Box>
      <DialogContent sx={{ pt: 2 }}>
        <Stack spacing={3}>
          <FormControl fullWidth>
            <Box display="flex" alignItems="center" mb={1}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                Number of Scenarios*
              </Typography>
              <Tooltip title="Amount of scenarios to generate. The more scenarios, the longer it will take to generate them." placement="right">
                <Image src="/tooltip-icon.svg" alt="Tooltip" width={18} height={18} style={{ marginLeft: 4 }} />
              </Tooltip>
            </Box>
            <TextField
              type="number"
              inputProps={{ min: 1, max: 50 }}
              value={numberOfScenarios}
              onChange={e => setNumberOfScenarios(Number(e.target.value))}
              required
              placeholder="Enter number of scenarios"
              label=""
            />
          </FormControl>
          <FormControl fullWidth>
            <Box display="flex" alignItems="center" mb={1}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                Instructions
              </Typography>
              <Tooltip title="Instructions for the scenarios, this will be used by the AI to generate the scenarios." placement="right">
                <Image src="/tooltip-icon.svg" alt="Tooltip" width={18} height={18} style={{ marginLeft: 4 }} />
              </Tooltip>
              <Box flex={1} />
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: 2 }}>
                <Box sx={{ position: 'relative' }}>
                  <Button
                    variant="outlined"
                    onClick={handleMenuClick}
                    sx={{
                      fontFamily: "Plus Jakarta Sans",
                      textTransform: "none",
                      fontSize: "14px",
                      color: "#7F56D9",
                      borderColor: "#7F56D9",
                      paddingRight: '36px', // Make room for the icon
                      '&:hover': {
                        borderColor: "#6941C6",
                        backgroundColor: "#F9F5FF"
                      }
                    }}
                  >
                    Templates
                  </Button>
                  <svg
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    data-testid="ArrowDropDownIcon"
                    style={{ position: 'absolute', right: '8px', top: '50%', transform: 'translateY(-50%)', pointerEvents: 'none' }}
                  >
                    <path
                      d="M18 9.00005C18 9.00005 13.5811 15 12 15C10.4188 15 6 9 6 9"
                      stroke="#141B34"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </Box>
                <Menu
                  anchorEl={anchorEl}
                  open={Boolean(anchorEl)}
                  onClose={handleMenuClose}
                  PaperProps={{
                    sx: {
                      mt: 1,
                      minWidth: 200,
                      borderRadius: '12px',
                      boxShadow: '0px 4px 6px -1px rgba(0, 0, 0, 0.1), 0px 2px 4px -1px rgba(0, 0, 0, 0.06)'
                    }
                  }}
                >
                  {templates.map((template) => (
                    <MenuItem
                      key={template.title}
                      onClick={() => handleTemplateSelect(template.description)}
                      sx={{
                        fontFamily: "Plus Jakarta Sans",
                        fontSize: "14px",
                        '&:hover': {
                          backgroundColor: "#F9F5FF"
                        }
                      }}
                    >
                      {template.title}
                    </MenuItem>
                  ))}
                </Menu>
              </Box>
            </Box>
            <TextField
              multiline
              rows={4}
              value={instructions}
              onChange={e => setInstructions(e.target.value)}
              placeholder="Enter instructions for the scenarios"
              label=""
            />
          </FormControl>
          <FormControl fullWidth required>
            <Box display="flex" alignItems="center" mb={1}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                Personality*
              </Typography>
              <Tooltip title="Select the personalities that the AI will use." placement="right">
                <Image src="/tooltip-icon.svg" alt="Tooltip" width={18} height={18} style={{ marginLeft: 4 }} />
              </Tooltip>
            </Box>
            <MUISelect
              value={personality}
              onChange={e => setPersonality(e.target.value)}
              required
              displayEmpty
            >
              <MenuItem value="" disabled>Select personality</MenuItem>
              <MenuItem value="American Male">American Male</MenuItem>
            </MUISelect>
          </FormControl>
          <FormControl fullWidth required>
            <Box display="flex" alignItems="center" mb={1}>
              <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                Metrics*
              </Typography>
              <Tooltip title="Metrics help you measure AI performance — track accuracy, response time, and key factors." placement="right">
                <Image src="/tooltip-icon.svg" alt="Tooltip" width={18} height={18} style={{ marginLeft: 4 }} />
              </Tooltip>
            </Box>
            {renderMetricsSelect({ metrics, selectedMetrics, setSelectedMetrics, setIsMetricsModalOpen, isMetricsModalOpen, fetchMetrics, MUISelect, Chip, CustomDropdownIcon })}
          </FormControl>
        </Stack>
      </DialogContent>
      <DialogActions sx={{ px: 3, pb: 3, pt: 2, borderTop: "none" }}>
        <Button
          fullWidth
          size="large"
          onClick={handleCreate}
          variant="contained"
          color="primary"
          startIcon={<RocketLaunchIcon sx={{ fontSize: 28 }} />}
          sx={{
            backgroundColor: "#7F56D9",
            borderRadius: "20px",
            height: "56px",
            fontFamily: "Plus Jakarta Sans",
            fontWeight: 500,
            fontSize: 20,
            color: "#fff",
            px: 4,
            boxShadow: 'none',
            textTransform: 'none',
            justifyContent: 'flex-start',
            '&:hover': {
              backgroundColor: "#6C47C2"
            }
          }}
          disabled={!submittable || isCreating}
        >
          {isCreating ? "Processing..." : "Create"}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default GenerateScenariosModal;

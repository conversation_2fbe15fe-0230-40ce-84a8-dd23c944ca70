.overallScoreCard {
  display: flex;
  align-items: center;
  background: #F7F8FA;
  border: 1px solid #E4E7EC;
  border-radius: 12px;
  padding: 10px;
  margin: 0 24px 24px 24px;
  cursor: pointer;
  transition: box-shadow 0.15s, border-color 0.15s;
}

.overallScoreCard:hover {
  box-shadow: 0 2px 8px rgba(80, 80, 120, 0.06);
  border-color: #C9CFE5;
}

.icon {
  width: 28px;
  height: 28px;
  margin-right: 5px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.label {
  font-size: 12px;
  font-weight: 600;
  color: #23272E;
  margin-right: 12px;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.value {
  font-size: 12px;
  font-weight: 700;
  color: #23272E;
  font-family: 'Plus Jakarta Sans', sans-serif;
}

.spacer {
  flex: 1;
}

.arrow {
  font-size: 24px;
  color: #23272E;
  margin-left: 12px;
  display: flex;
  align-items: center;
} 
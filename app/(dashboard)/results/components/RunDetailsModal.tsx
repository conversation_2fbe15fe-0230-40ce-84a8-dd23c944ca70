"use client";


import styles from "./RunDetailsModal.module.scss";
import { IRun } from "@/types/runs";
import { useEffect, useRef, useState } from "react";
import { useNotification } from "@/context/NotificationContext/NotificationContext";
import useResultsAnalytics from "@/app/(dashboard)/results/useResultsAnalytics";
import useGeneralAnalyticsEvents from "@/utils/useGeneralAnalyticsEvents";
import ChatBubbleList from "@/components/ChatBubbleList/ChatBubbleList";
import CustomAudioPlayer from "@/components/AudioPlayer/AudioPlayer";
import { Typography, Drawer, Box, IconButton, CircularProgress, useMediaQuery } from "@mui/material";
import CloseIcon from '@mui/icons-material/Close';
import MetricsItemList from "@/components/MetricsItemList/MetricsItemList";
import RunDetailsInfoCard from './RunDetailsInfoCard';
import OverallScoreCard from './OverallScoreCard';
import Image from "next/image";

interface IGenerateScenariosModalProps {
  onCancel: () => void;
  run: IRun;
}

interface MetricItem {
  id: string;
  label: string;
  state: number;
}

const RunDetailsModal = ({ onCancel, run }: IGenerateScenariosModalProps) => {
  const generalAnalyticsEvents = useGeneralAnalyticsEvents();
  const scrollRef = useRef(null);
  const [modalOpenedTime, setModalOpenedTime] = useState<number | null>(null);
  const resultAnalytics = useResultsAnalytics();
  const [loading, setLoading] = useState(false);
  const [metrics, setMetrics] = useState<MetricItem[]>([]);
  const notify = useNotification();
  const [audioSrc, setAudioSrc] = useState<string | null>(null);
  const isMobile = useMediaQuery('(max-width=600px)');
  const [step, setStep] = useState(0);

  useEffect(() => {
    if (!run || !run.metrics || typeof run.metrics !== "object") {
      setMetrics([]);
      return;
    }

    const metricsData: MetricItem[] = Object.keys(run.metrics).map((key, index) => {
      const value = run.metrics ? run.metrics[key as keyof typeof run.metrics] : null;
      return {
        id: `${index + 1}`,
        label: key,
        state: typeof value === "number" ? value : 0,
      };
    });

    setMetrics(metricsData);
  }, [run]);


  useEffect(() => {
    resultAnalytics.trackCallDetailsViewed(run);
    setModalOpenedTime(Date.now());
    resultAnalytics.trackViewedRunDetails();
  }, [resultAnalytics, run]);

  const downloadAndPlayAudio = async () => {
    const accountSid = "**********************************";
    const authToken = "222399157cf4a449fea6b98469002e48";
    if (!run.recordingUrl) {
      console.error("No recording link found");
      return;
    }
    try {
      setLoading(true);
      const response = await fetch(run.recordingUrl, {
        headers: {
          Authorization: `Basic ${btoa(`${accountSid}:${authToken}`)}`,
        },
      });
      if (!response.ok) {
        setLoading(false);
        notify.error({
          message: "Failed to download audio",
        });
        return;
      }
      const blob = await response.blob();
      const audioUrl = URL.createObjectURL(blob);
      setAudioSrc(audioUrl);
      setLoading(false);
    } catch (error) {
      setLoading(false);
      console.error("Error downloading or playing audio:", error);
    }
  };

  useEffect(() => {
    downloadAndPlayAudio();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (loading) {
    return (
      <Box display="flex" alignItems="center" justifyContent="center" height="100%">
        <CircularProgress />
      </Box>
    );
  }


  const handleCancel = () => {
    const timeSpent = modalOpenedTime ? Date.now() - modalOpenedTime : 0;
    generalAnalyticsEvents.trackModalCloseButtonClicked(
      "RunDetailsModal",
      timeSpent,
      "viewed",
    );
    onCancel();
  };

  return (
    <Drawer
      anchor="right"
      open={!!run}
      onClose={handleCancel}
      hideBackdrop
      PaperProps={{
        sx: {
          width: '100%',
          maxWidth: 600,
          padding: 2,
          position: 'relative',
          boxShadow: '0 8px 32px rgba(16, 24, 40, 0.18)',
        },
      }}
      sx={{
        '& .MuiDrawer-paperAnchorRight': {
          right: 20,
          left: 'auto',
          borderRadius: '30px',
        },
        '&.MuiDrawer-root': {
          left: 'auto !important',
          width: isMobile ? '100%' : 600,
        },
      }}
    >
      <Box sx={{ fontFamily: 'Plus Jakarta Sans, sans-serif', position: 'relative', height: '100%', display: 'flex', flexDirection: 'column' }}>
        {/* Close Button */}
        <IconButton
          onClick={handleCancel}
          sx={{ position: 'absolute', top: 16, right: 16, zIndex: 2, fontFamily: 'Plus Jakarta Sans, sans-serif' }}
          aria-label="close"
        >
          <CloseIcon fontSize="large" />
        </IconButton>
        {/* Title (dynamic by step) */}
        {step === 0 && (
          <Typography variant="h6" fontWeight="bold" sx={{ pt: 4, pb: 2, pl: 3, pr: 6, fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
            Run Detail
          </Typography>
        )}
        {step === 1 && (
          <Box sx={{ display: 'flex', alignItems: 'center', pt: 4, pb: 2, pl: 3, pr: 6 }}>
            <IconButton onClick={() => setStep(0)} sx={{ mr: 1 }} aria-label="back">
              <Image src="/chevron-down.svg" alt="Chevron Down" width={24} height={24} />
            </IconButton>
            <Typography sx={{ fontFamily: 'Plus Jakarta Sans, sans-serif', fontSize: 16, fontWeight: 600 }}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                Overall Score <Image src="/Rectangle.svg" alt="Rectangle" width={24} height={20} />
                <span style={{ color: "#7F56D9", fontSize: 16, fontWeight: 700 }}>{typeof run.score === 'number' ? run.score : 0}%</span>
              </Box>

            </Typography>
          </Box>
        )}
        {step === 2 && (
          <Box sx={{ display: 'flex', pt: 4, pb: 2, pl: 3, pr: 6 }}>
            <IconButton onClick={() => setStep(0)} sx={{ mr: 1 }} aria-label="back">
              <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15 18L9 12L15 6" stroke="#23272E" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </IconButton>
            <Typography variant="h6" fontWeight="bold" sx={{ fontFamily: 'Plus Jakarta Sans, sans-serif' }}>
              Transcription
            </Typography>
          </Box>
        )}
        {step === 0 && (
          <>
            <RunDetailsInfoCard
              id={run.id ? String(run.id) : run.callId ? String(run.callId) : undefined}
              latency={run.latency}
              timestamp={run.timestamp}
            />
            <OverallScoreCard score={typeof run.score === 'number' ? run.score : 0} onClick={() => setStep(1)} />
            {/* Content */}
            <Box className={styles.modalBodyWrapper} ref={scrollRef} sx={{ flex: 1, overflow: 'hidden', pb: '200px' }}>
              <Box className={styles.scrollableContent} sx={{ pr: 3, pl: 3 }}>
                {/* Transcription Section Header with Expand Button */}
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mt: 3, mb: 1 }}>
                  <Typography fontWeight="bold" sx={{ fontFamily: 'Plus Jakarta Sans, sans-serif', fontSize: 14 }}>
                    Transcript
                  </Typography>
                  <IconButton
                    onClick={() => setStep(2)}
                    sx={{
                      background: '#F7F8FA',
                      borderRadius: '10px',
                      width: 36,
                      height: 36,
                      boxShadow: 'none',
                      border: 'none',
                      p: 0,
                      '&:hover': { background: '#ECECEC' },
                    }}
                    aria-label="Expand Transcript"
                  >
                    <Image src="/resize-transcript.svg" alt="Expand Transcript" width={14} height={14} />
                  </IconButton>
                </Box>

                <ChatBubbleList segments={Array.isArray(run.transcription.segments) ? run.transcription.segments.map(segment => ({
                  ...segment,
                  speaker: segment.speaker,
                  transcript: segment.transcript || ''
                })) : []} />
              </Box>
            </Box>
          </>
        )}
        {step === 1 && (
          <Box sx={{ pr: 3, pl: 3, display: 'flex', width: '100%', pb: '200px', overflowY: 'auto' }}>
            <MetricsItemList items={metrics} />
          </Box>
        )}
        {step === 2 && (
          <Box sx={{
            flex: 1,
            display: 'flex',
            flexDirection: 'column',
            minHeight: 0,
            p: 4,
            pb: '200px', // leave space for audio player
          }}>
            <Box sx={{ flex: 1, minHeight: 0, overflowY: 'auto' }}>
              <ChatBubbleList segments={Array.isArray(run.transcription.segments) ? run.transcription.segments.map(segment => ({
                ...segment,
                speaker: segment.speaker,
                transcript: segment.transcript || ''
              })) : []} />
            </Box>
          </Box>
        )}
        {/* Audio Player Fixed at Bottom */}
        <Box className={styles.audioPlayerFixed} sx={{ position: 'absolute', left: 0, bottom: 0, width: '100%', zIndex: 1 }}>
          <CustomAudioPlayer
            title=""
            subtitle="Call Recording"
            coverUrl="/avatar.svg"
            src={audioSrc || ""}
          />
        </Box>
      </Box>
    </Drawer>
  );
};

export default RunDetailsModal;

"use client";
import React from 'react';
import { Box } from '@mui/material';
import DashboardHeader from './DashboardHeader';
import DashboardKpiCards from './DashboardKpiCards';
import { DashboardStats } from '@/hooks/useDashboard';
import DashboardEmptyState from './DashboardEmptyState';
import DashboardBarChart from './DashboardBarChart';

interface DashboardSectionProps {
  stats: DashboardStats | null;
}

const DashboardSection: React.FC<DashboardSectionProps> = ({ stats }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        gap: 3,
        marginTop: '32px'
      }}
    >
      <DashboardHeader activeRuns={stats?.no_of_calls || 0} />

      <DashboardKpiCards
        totalRuns={stats?.no_of_calls || 0}
        averageDuration={stats?.avg_duration || '0s'}
        failureRate={stats?.failed_calls_average || '0%'}
      />
      {stats && stats?.chart_data.length > 0 ? <DashboardBarChart
        data={stats?.chart_data || []}
        title=""
        dataKey="runs"
        barColor="#A689FA"
      /> : <DashboardEmptyState />}
    </Box>
  );
};

export default DashboardSection;

"use client";
import React from 'react';
import { Box, Typography } from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YA<PERSON>s,
  Tooltip,
  ResponsiveContainer,
  CartesianGrid,
} from 'recharts';

interface ChartDataPoint {
  month: string;
  year: number;
  runs?: number;
}


interface DashboardBarChartProps {
  data: ChartDataPoint[];
  title?: string;
  dataKey?: string;
  barColor?: string;
  xAxisTickFormatter?: (month: string) => string;
}

const CustomBarTooltip = ({ active, payload, label }: { active: boolean, payload: any[], label: string }) => {
  if (!active || !payload || !payload.length) return null;
  const item = payload[0];
  const { year } = item.payload;

  return (
    <div
      style={{
        background: 'rgba(255,255,255,0.1)',
        backdropFilter: 'blur(10px)',
        WebkitBackdropFilter: 'blur(10px)',
        borderRadius: 16,
        border: '1px solid #E0E0E0',
        boxShadow: '0 2px 8px rgba(0,0,0,0.08)',
        fontFamily: 'Plus Jakarta Sans',
        padding: '16px 20px',
        minWidth: 200,
      }}
    >
      <div
        style={{
          fontWeight: 700,
          fontSize: 16,
          color: '#232B29',
          marginBottom: 12,
        }}
      >
        {label} {year}
      </div>
      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
        <div
          style={{
            width: 6,
            height: 32,
            borderRadius: 4,
            background: '#9E77ED',
            marginRight: 12,
          }}
        />
        <div
          style={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 500,
            fontSize: 14,
            lineHeight: '18px',
            color: '#414346',
            flex: 1,
          }}
        >
          Number of Runs
        </div>
        <div
          style={{
            fontFamily: 'Plus Jakarta Sans',
            fontWeight: 700,
            fontSize: 12,
            lineHeight: '18px',
            color: '#414346',
            textAlign: 'right',
            minWidth: 40,
          }}
        >
          {item.value}
        </div>
      </div>
    </div>
  );
};

// Define the bar chart in a reusable component
const DashboardBarChart: React.FC<DashboardBarChartProps> = ({
  data,
  title = 'Latency',
  dataKey = 'total_latency_ms',
  xAxisTickFormatter,
}) => {

  return (
    <Box
      sx={{
        p: 3,
        borderRadius: '34px',
        backgroundColor: '#FFFFFF',
        border: '1px solid #E5E6E6',
        overflow: 'hidden',
      }}
    >
      {/* Title / Section Heading */}
      <Typography 
        sx={{ 
          mb: 3, 
          fontSize: '16px',
          fontWeight: 'bold',
          fontFamily: 'Plus Jakarta Sans',
        }}
      >
        {title}
      </Typography>

      <Box sx={{ width: '100%', height: 360 }}>
        <ResponsiveContainer>
          <BarChart
            data={data}
            margin={{ top: 0, right: 0, bottom: 0, left: 0 }}
            barGap={0}
            barCategoryGap="5%"
          >
            {/* Light grid lines */}
            <CartesianGrid
              vertical={false}
              stroke="#E6E6E6"
              strokeOpacity={0.7}
              strokeWidth={1}
            />

            {/* X-axis for months */}
            <XAxis
              dataKey="month"
              tickLine={false}
              axisLine={false}
              stroke="#414346"
              tick={{
                fontFamily: 'Plus Jakarta Sans',
                fontWeight: 600,
                fontSize: 14,
                letterSpacing: 0,
              }}
              tickFormatter={xAxisTickFormatter}
            />

            {/* Y-axis for runs (auto domain based on data) */}
            <YAxis
              tickLine={false}
              axisLine={false}
              stroke="#414346"
              tick={{ 
                fontFamily: 'Plus Jakarta Sans',
                fontSize: 14,
                fontWeight: 600,
              }}
            />

            {/* Tooltip for month, year, runs */}
            <Tooltip
              cursor={{ fill: 'transparent' }}
              content={<CustomBarTooltip active={true} payload={[]} label={''} />}
            />

            {/* First Bar Group */}
            <Bar
              dataKey={dataKey}
              name={title}
              radius={[6, 0, 0, 0]}
              fill={'#9E77ED'}
              barSize={48}
              shape={(props: any) => {
                const { x, y, width, height } = props;
                return (
                  <rect
                    x={x}
                    y={y}
                    width={width}
                    height={height}
                    fill={'#9E77ED'}
                    ry={6}
                  />
                );
              }}
            />
          </BarChart>
        </ResponsiveContainer>
      </Box>
    </Box>
  );
};

export default DashboardBarChart;

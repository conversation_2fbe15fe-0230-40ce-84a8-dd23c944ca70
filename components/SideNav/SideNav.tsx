import React, { useState } from "react";
import {
  Drawer,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  IconButton,
  Box,
  Typography,
  Button,
  Stack,
  Select,
  FormControl,
  InputLabel,
  SelectChangeEvent,
  MenuItem,
} from "@mui/material";
import {
  ChevronLeft as ChevronLeftIcon,
  ChevronRight as ChevronRightIcon,
  Close as CloseIcon,
} from "@mui/icons-material";
import { useSidebar } from "@/providers/side-menu-context";
import { usePathname, useRouter } from "next/navigation";
import Routes from "@/constants/routes";
import LogoBeta from "../Logo-beta/Logo-beta";
import Image from "next/image";

import ResultsIcon from "../../public/side-nav/results-icon.svg";
import ResultsIconActive from "../../public/side-nav/results-icon-active.svg";
import HomeSvg from "../../public/side-nav/elements.svg";
import HomeSvgActive from "../../public/side-nav/home-icon-active.svg";
import MetricsSvg from "../../public/side-nav/task-square.svg";
import MetricsSvgActive from "../../public/side-nav/metrics-icon-active.svg";
import AgentSvg from "../../public/side-nav/agent-icon.svg";
import AgentSvgActive from "../../public/side-nav/agents-icon-active.svg";
import ScenarioSvg from "../../public/side-nav/scenario-icon.svg";
import ScenarioSvgActive from "../../public/side-nav/scenario-icon-active.svg";
import PersonalitySvg from "../../public/side-nav/ai-voice.svg";
import PersonalitySvgActive from "../../public/side-nav/personality-icon-active.svg";
import { useGeneralStore } from "@/providers/general-store-provider";
import useSectionEngagementAnalytics from "../SideMenu/useSectionEngagementAnalytics";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import { useAuthStore } from "@/stores/auth-store";
import UpdateAgentModal from "@/app/features/Agent/components/UpdateAgentModal/UpdateAgentModal";


const drawerWidth = 269;
const collapsedWidth = 80;

const SideNav = () => {
  const router = useRouter();
  const pathname = usePathname();
  const { toggleSidebar, isSidebarOpen } = useSidebar();
  const [showPremiumPopup, setShowPremiumPopup] = useState(true);
  const [isLogoHovered, setIsLogoHovered] = useState(false);
  const [updateAgent, setUpdateAgent] = useState<string | null>(null);
  const { currentAgentId, setCurrentAgentId, agents } = useGeneralStore(
    (state) => state,
  );
  const sectionEngagementAnalytics = useSectionEngagementAnalytics();

  const { user } = useAuthStore((state) => state);
  const isAdmin = user?.isAdministrator;

  const menuItems = [
    { text: "Dashboard", icon: HomeSvg, activeIcon: HomeSvgActive, path: Routes.home },
    { text: "Agents", icon: AgentSvg, activeIcon: AgentSvgActive, path: Routes.agents },
    { text: "Metrics", icon: MetricsSvg, activeIcon: MetricsSvgActive, path: Routes.metricsMetrics },
    { text: "Personality", icon: PersonalitySvg, activeIcon: PersonalitySvgActive, path: Routes.personality },
    { text: "Scenario", icon: ScenarioSvg, activeIcon: ScenarioSvgActive, path: Routes.scenario },
    { text: "Results", icon: ResultsIcon, activeIcon: ResultsIconActive, path: Routes.results },
    ...(isAdmin ? [
      { text: "Users", icon: ResultsIcon, activeIcon: ResultsIconActive, path: 'admin/users' }
    ] : [])
  ];


  const handleChange = (id: string) => {
    setCurrentAgentId(id);
  };

  const handleDrawerToggle = () => {
    toggleSidebar();
  };

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const handleLogoMouseEnter = () => {
    setIsLogoHovered(true);
  };

  const handleLogoMouseLeave = () => {
    setIsLogoHovered(false);
  };
  const handleSelectChange = (event: SelectChangeEvent<string>) => {
    const id = event.target.value;
    sectionEngagementAnalytics.trackAgentSwitched(currentAgentId, id);
    if (id === "create") {
      setUpdateAgent("new");
    } else {
      handleChange(id);
    }
  };

  return (
    <Drawer
      variant="permanent"
      sx={{
        width: isSidebarOpen ? drawerWidth : collapsedWidth,
        flexShrink: 0,
        "& .MuiDrawer-paper": {
          height: "calc(100% - 2rem)",
          width: isSidebarOpen ? drawerWidth : collapsedWidth,
          boxSizing: "border-box",
          backgroundColor: "#fff",
          borderRight: "none",
          transition: "width 0.3s ease",
          overflowX: "hidden",
          zIndex: 999,
          margin: "1rem 1rem",
          borderRadius: "32px",
        },
      }}
    >
      {/* Logo Section */}
      <Box
        sx={{
          display: "flex",
          alignItems: "center",
          justifyContent: isSidebarOpen ? "space-between" : "center",
          p: 2,
        }}
      >
        {isSidebarOpen ? (
          <>
            <Box
              sx={{ display: "flex", flexDirection: "column", width: "100%" }}
            >
              <Box
                sx={{
                  display: "flex",
                  justifyContent: "space-between",
                  width: "100%",
                  alignItems: "center",
                }}
              >
                <LogoBeta />
                <IconButton
                  onClick={handleDrawerToggle}
                  sx={{
                    backgroundColor: "#fff",
                    width: 24,
                    height: 24,
                    borderRadius: "50%",
                    color: "#CFCFCF",
                    border: "1px solid #CFCFCF",
                    transition: "background-color 0.3s ease",
                    "&:hover": {
                      color: "#fff",
                      border: "1px solid #CFCFCF",
                      backgroundColor: "#CFCFCF",
                    },
                  }}
                >
                  <ChevronLeftIcon fontSize="small" />
                </IconButton>
              </Box>
              <Box>
                <Stack
                  direction="row"
                  spacing={1}
                  alignItems="center"
                  id="step1"
                  sx={{ width: "100%" }}
                >
                  <FormControl
                    variant="outlined"
                    size="small"
                    sx={{ flexGrow: 1, justifyContent: "center" }}
                  >
                    <InputLabel id="agent-select-label">Agent</InputLabel>
                    <Select
                      labelId="agent-select-label"
                      value={currentAgentId}
                      onChange={handleSelectChange}
                      label="Agent"
                      sx={{
                        width: "100%",
                        height: "48px",
                        backgroundColor: "white",
                        borderRadius: "100px",
                        boxShadow: "0px 1px 2px rgba(16, 24, 40, 0.05)",
                        "& .MuiOutlinedInput-notchedOutline": {
                          border: "1px solid #E5E7EB",
                          borderRadius: "100px",
                        },
                        "&:hover .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E5E7EB",
                        },
                        "&.Mui-focused .MuiOutlinedInput-notchedOutline": {
                          borderColor: "#E5E7EB",
                        },
                        "& .MuiSelect-select": {
                          paddingLeft: "16px",
                          fontFamily: "Plus Jakarta Sans",
                          fontSize: "14px",
                          fontWeight: 500,
                          color: "#111827",
                        },
                        "& .MuiSelect-icon": {
                          right: "16px",
                          color: "#6B7280",
                        },
                      }}
                      MenuProps={{
                        PaperProps: {
                          sx: {
                            mt: 1,
                            boxShadow:
                              "0px 4px 6px -2px rgba(16, 24, 40, 0.03), 0px 12px 16px -4px rgba(16, 24, 40, 0.08)",
                            borderRadius: "12px",
                            "& .MuiMenuItem-root": {
                              fontFamily: "Plus Jakarta Sans",
                              fontSize: "14px",
                              fontWeight: 500,
                              color: "#111827",
                              padding: "10px 16px",
                              "&:hover": {
                                backgroundColor: "#F9FAFB",
                              },
                            },
                          },
                        },
                      }}
                      renderValue={(value) => {
                        const selected = agents.find((agent) => agent.id === value);
                        if (!selected) return "";
                        const nameArr = Array.from(selected.name);
                        return nameArr.length > 20 ? nameArr.slice(0, 20).join('') + '…' : selected.name;
                      }}
                      onClick={() =>
                        sectionEngagementAnalytics.trackAgentSelectorClicked(
                          currentAgentId,
                          agents.length,
                        )
                      }
                    >
                      <MenuItem value="create">
                        <Stack direction="row" spacing={1} alignItems="center">
                          <AddIcon sx={{ fontSize: 20, color: "#6B7280" }} />
                          <Typography sx={{
                            fontFamily: "Plus Jakarta Sans",
                            fontSize: "14px",
                            fontWeight: 500,
                            color: "#111827"
                          }}>
                            Create
                          </Typography>
                        </Stack>
                      </MenuItem>
                      {Array.isArray(agents) && agents.map((agent) => (
                        <MenuItem
                          key={agent.id}
                          value={agent.id}
                          sx={{
                            display: "flex",
                            justifyContent: "space-between",
                            alignItems: "center",
                            "&:hover .edit-icon": {
                              visibility: "visible",
                            },
                          }}
                        >
                          <Typography sx={{
                            fontFamily: "Plus Jakarta Sans",
                            fontSize: "14px",
                            fontWeight: 500,
                            color: "#111827"
                          }}>
                            {Array.from(agent.name).length > 25
                              ? Array.from(agent.name).slice(0, 25).join('') + '…'
                              : agent.name}
                          </Typography>
                          <IconButton
                            className="edit-icon"
                            size="small"
                            onClick={(e) => {
                              e.stopPropagation();
                              setUpdateAgent(agent.id);
                            }}
                            sx={{
                              visibility: "hidden",
                              width: 28,
                              height: 28,
                              color: "#6B7280",
                              "&:hover": {
                                backgroundColor: "#F3F4F6",
                              },
                            }}
                          >
                            <EditIcon sx={{ fontSize: 16 }} />
                          </IconButton>
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Stack>
              </Box>
            </Box>
          </>
        ) : (
          <Box
            onMouseEnter={handleLogoMouseEnter}
            onMouseLeave={handleLogoMouseLeave}
            sx={{
              display: "flex",
              alignItems: "center",
              justifyContent: "center",
              width: 25,
              height: 25,
              position: "relative",
            }}
          >
            <Box
              sx={{
                opacity: isLogoHovered ? 0 : 1,
                transition: "opacity 0.2s ease",
                position: "absolute",
              }}
            >
              <Image
                src="/small-logo.svg"
                alt="Logo"
                width={25}
                height={25}
                style={{ borderRadius: "50%" }}
              />
            </Box>
            <Box
              sx={{
                opacity: isLogoHovered ? 1 : 0,
                transition: "opacity 0.2s ease",
                position: "absolute",
              }}
            >
              <IconButton
                onClick={handleDrawerToggle}
                sx={{
                  backgroundColor: "#fff",
                  width: 24,
                  height: 24,
                  borderRadius: "50%",
                  color: "#CFCFCF",
                  border: "1px solid #CFCFCF",
                  transition: "background-color 0.3s ease",
                  "&:hover": {
                    color: "#fff",
                    border: "1px solid #CFCFCF",
                    backgroundColor: "#CFCFCF",
                  },
                }}
              >
                <ChevronRightIcon fontSize="small" />
              </IconButton>
            </Box>
          </Box>
        )}
      </Box>

      <List sx={{ display: "flex", flexDirection: "column", alignItems: "center", px: 2, gap: '8px' }}>
        {menuItems.map((item) => {
          const IconComponent = item.icon;
          const ActiveIconComponent = item.activeIcon;
          const isActive = pathname === item.path;

          return (
            <ListItem
              component="button"
              key={item.text}
              onClick={() => handleNavigation(item.path)}
              sx={{
                border: "none",
                cursor: "pointer",
                justifyContent: isSidebarOpen ? "initial" : "center",
                px: isSidebarOpen ? 4 : 1.5,
                py: 1,
                width: "100%",
                height: "52px",
                borderRadius: "18px",
                mx: isSidebarOpen ? 1 : 0,
                backgroundColor: isActive ? "#000" : "transparent",
                "&:hover": {
                  backgroundColor: isActive ? "#000" : "#f5f5f5",
                },
              }}
            >
              <ListItemIcon
                sx={{
                  minWidth: isSidebarOpen ? 32 : "auto",
                  fontFamily: "Plus Jakarta Sans",
                }}
              >
                {isActive && ActiveIconComponent ? (
                  <ActiveIconComponent
                    style={{
                      width: 24,
                      height: 24,
                      fill: "#fff",
                      stroke: "#fff",
                    }}
                  />
                ) : (
                  <IconComponent
                    style={{
                      width: 24,
                      height: 24,
                      fill: isActive ? "#fff" : undefined,
                      stroke: isActive ? "#fff" : undefined,
                    }}
                  />
                )}
              </ListItemIcon>
              {isSidebarOpen && (
                <ListItemText
                  primary={item.text}
                  sx={{
                    opacity: isSidebarOpen ? 1 : 0,
                    "& .MuiTypography-root": {
                      cursor: "pointer",
                      fontSize: "0.8rem",
                      fontWeight: 500,
                      fontFamily: "Plus Jakarta Sans",
                      color: isActive ? "#fff" : "#667085",
                    },
                  }}
                />
              )}
            </ListItem>
          );
        })}
      </List>
      {updateAgent && (
        <UpdateAgentModal
          agentId={updateAgent === "new" ? null : updateAgent}
          isModalOpen={!!updateAgent}
          onCancel={() => setUpdateAgent(null)}
        />
      )}

      {showPremiumPopup && (
        <Box
          data-testid="premium-popup"
          sx={{
            position: isSidebarOpen ? "absolute" : "fixed",
            bottom: 32,
            left: isSidebarOpen ? "auto" : 112,
            minHeight: 272,
            right: isSidebarOpen ? 16 : "auto",
            width: isSidebarOpen ? "auto" : 240,
            backgroundColor: "#9E77ED",
            color: "#fff",
            p: "24px",
            borderRadius: "24px",
            backgroundImage: "url('/group.svg')",
            backgroundRepeat: "no-repeat",
            backgroundPosition: "right -20px top 10px",
            backgroundSize: "50%",
            overflow: "hidden",
            zIndex: 1002,
          }}
        >
          <IconButton
            onClick={() => setShowPremiumPopup(false)}
            sx={{
              position: "absolute",
              top: "15px",
              right: "15px",
              color: "#101828",
              padding: "8px",
              backgroundColor: "#fff",
              width: "24px",
              height: "24px",
              "&:hover": {
                backgroundColor: "#fff",
              },
            }}
          >
            <CloseIcon sx={{
              fontSize: 15,
              strokeWidth: 1.5,
              stroke: "currentColor"
            }} />
          </IconButton>
          <Typography
            variant="h6"
            sx={{
              fontWeight: 800,
              fontSize: "24px",
              lineHeight: "32px",
              mb: "8px",
              mt: "24px",
              fontFamily: "Plus Jakarta Sans",
              letterSpacing: "-0.02em",
            }}
          >
            Test AI Agents in Minutes, Not Months
          </Typography>
          <Typography
            variant="body2"
            sx={{
              fontSize: "14px",
              lineHeight: "24px",
              fontWeight: 500,
              mb: "12px",
              color: "#fff",
              fontFamily: "Plus Jakarta Sans",
            }}
          >
            Launch, automate, and grow—without writing a single line of code.
          </Typography>
          <Button
            variant="contained"
            fullWidth
            sx={{
              backgroundColor: "#fff",
              color: "#101828",
              textTransform: "none",
              fontWeight: 600,
              fontSize: "16px",
              lineHeight: "24px",
              padding: "16px 20px",
              borderRadius: "16px",
              fontFamily: "Plus Jakarta Sans",
              "&:hover": {
                backgroundColor: "#f0f0f0",
              },
            }}
            onClick={() => router.push(Routes.scenario)}
          >
            Test More
          </Button>
        </Box>
      )}
    </Drawer>
  );
};

export default SideNav;

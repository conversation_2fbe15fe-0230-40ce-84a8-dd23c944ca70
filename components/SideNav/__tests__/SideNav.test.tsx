import React from 'react';
import { render, screen } from '@testing-library/react';
import { ThemeProvider } from '@mui/material/styles';
import { createTheme } from '@mui/material/styles';
import SideNav from '../SideNav';
import { SidebarProvider } from '@/providers/side-menu-context';
import { useRouter } from 'next/navigation';
import { useAuthStore } from '@/stores/auth-store';
import { useGeneralStore } from '@/providers/general-store-provider';

// Mock the dependencies
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  usePathname: jest.fn(() => '/home'),
}));

jest.mock('@/stores/auth-store', () => ({
  useAuthStore: jest.fn(),
}));

jest.mock('@/providers/general-store-provider', () => ({
  useGeneralStore: jest.fn(),
}));

jest.mock('../useSectionEngagementAnalytics', () => ({
  __esModule: true,
  default: () => ({
    trackAgentSwitched: jest.fn(),
    trackAgentSelectorClicked: jest.fn(),
  }),
}));

const theme = createTheme();

const mockRouter = {
  push: jest.fn(),
  replace: jest.fn(),
  back: jest.fn(),
  forward: jest.fn(),
  refresh: jest.fn(),
  prefetch: jest.fn(),
};

const mockAuthStore = {
  user: {
    id: '1',
    email: '<EMAIL>',
    isAdministrator: false,
  },
};

const mockGeneralStore = {
  currentAgentId: 'agent-1',
  setCurrentAgentId: jest.fn(),
  agents: [
    { id: 'agent-1', name: 'Test Agent 1' },
    { id: 'agent-2', name: 'Test Agent 2' },
  ],
};

const TestWrapper = ({ children, isSidebarOpen = true }: { children: React.ReactNode; isSidebarOpen?: boolean }) => {
  return (
    <ThemeProvider theme={theme}>
      <SidebarProvider>
        <div data-testid="sidebar-context" data-sidebar-open={isSidebarOpen}>
          {children}
        </div>
      </SidebarProvider>
    </ThemeProvider>
  );
};

describe('SideNav Component', () => {
  beforeEach(() => {
    (useRouter as jest.Mock).mockReturnValue(mockRouter);
    (useAuthStore as jest.Mock).mockReturnValue(mockAuthStore);
    (useGeneralStore as jest.Mock).mockReturnValue(mockGeneralStore);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders premium popup when sidebar is open', () => {
    render(
      <TestWrapper isSidebarOpen={true}>
        <SideNav />
      </TestWrapper>
    );

    // The premium popup should be visible when sidebar is open
    expect(screen.getByText('Test AI Agents in Minutes, Not Months')).toBeInTheDocument();
    expect(screen.getByText('Launch, automate, and grow—without writing a single line of code.')).toBeInTheDocument();
  });

  it('renders premium popup when sidebar is collapsed', () => {
    render(
      <TestWrapper isSidebarOpen={false}>
        <SideNav />
      </TestWrapper>
    );

    // The premium popup should still be visible when sidebar is collapsed (this is our fix)
    expect(screen.getByText('Test AI Agents in Minutes, Not Months')).toBeInTheDocument();
    expect(screen.getByText('Launch, automate, and grow—without writing a single line of code.')).toBeInTheDocument();
  });

  it('applies correct positioning styles when sidebar is collapsed', () => {
    const { container } = render(
      <TestWrapper isSidebarOpen={false}>
        <SideNav />
      </TestWrapper>
    );

    // Find the premium popup box
    const popupBox = container.querySelector('[data-testid="premium-popup"]') ||
                     container.querySelector('div[style*="position: absolute"]');

    if (popupBox) {
      const styles = window.getComputedStyle(popupBox);
      // When collapsed, the popup should have different positioning
      expect(styles.position).toBe('absolute');
    }
  });

  it('applies correct positioning styles when sidebar is open', () => {
    const { container } = render(
      <TestWrapper isSidebarOpen={true}>
        <SideNav />
      </TestWrapper>
    );

    // Find the premium popup box
    const popupBox = container.querySelector('[data-testid="premium-popup"]') ||
                     container.querySelector('div[style*="position: absolute"]');

    if (popupBox) {
      const styles = window.getComputedStyle(popupBox);
      expect(styles.position).toBe('absolute');
    }
  });
});
